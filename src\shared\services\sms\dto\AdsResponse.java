package com.redon_agency.chatbot.common.feign.sms.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.io.Serializable;

@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AdsResponse implements Serializable {
    @JsonProperty("BatchId")
    private String batchId;         // BatchId của tin nhắn quảng cáo

    @JsonProperty("NumMessageSent")
    private int numMessageSent;     // Số lượng tin đã gửi

    @JsonProperty("NumRemainQuota")
    private int numRemainQuota;     // Lượng quota còn lại
}
