package com.redon_agency.chatbot.common.feign.sms.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.io.Serializable;

@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CampaignDlr implements Serializable {
    @JsonProperty("CampaignCode")
    private String campaignCode;

    @JsonProperty("ReasonDescription")
    private String reasonDescription;
}
