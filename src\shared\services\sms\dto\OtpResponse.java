package com.redon_agency.chatbot.common.feign.sms.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.io.Serializable;

@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OtpResponse implements Serializable {
    @JsonProperty("MessageId")
    private String messageId;

    @JsonProperty("BrandName")
    private String brandName;

    @JsonProperty("Phone")
    private String phone;

    @JsonProperty("Message")
    private String message;

    @JsonProperty("PartnerId")
    private String partnerId;

    @JsonProperty("Telco")
    private String telco;
}
