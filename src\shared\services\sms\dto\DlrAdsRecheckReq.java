package com.redon_agency.chatbot.common.feign.sms.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DlrAdsRecheckReq {
    @JsonProperty("access_token")
    private String accessToken;

    @JsonProperty("FromDate")
    private String fromDate; // yyyy-MM-dd

    @JsonProperty("ToDate")
    private String toDate; // yyyy-MM-dd
}
