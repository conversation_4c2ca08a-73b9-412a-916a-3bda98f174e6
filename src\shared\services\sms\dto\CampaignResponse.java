package com.redon_agency.chatbot.common.feign.sms.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.io.Serializable;

@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CampaignResponse implements Serializable {
    @JsonProperty("CampaignCode")
    private String campaignCode;  // Mã số của campaign

    @JsonProperty("error")
    private String error;  // Mã số của campaign

    @JsonProperty("error_description")
    private String errorDescription;  // Mã số của campaign
}
