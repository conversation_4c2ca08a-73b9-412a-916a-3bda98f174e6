package com.redon_agency.chatbot.common.feign.sms.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.io.Serializable;

@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OtpRequest implements Serializable {
    @JsonProperty("access_token")
    private String accessToken;

    @JsonProperty("session_id")
    private String sessionId;

    @JsonProperty("BrandName")
    private String brandName;  // BrandName đã đăng ký

    @JsonProperty("Phone")
    private String phone;      // Số điện thoại, định dạng 84xxx hoặc 0xxx

    @JsonProperty("Message")
    private String message;    // Nội dung tin nhắn, cần được mã hóa Base64 nếu yêu cầu
}
