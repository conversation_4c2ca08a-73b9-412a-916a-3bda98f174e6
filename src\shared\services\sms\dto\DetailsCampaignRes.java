package com.redon_agency.chatbot.common.feign.sms.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.util.List;

@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DetailsCampaignRes {
    @JsonProperty("CampaignCode")
    private String campaignCode;

    @JsonProperty("Total")
    private String total;

    @JsonProperty("Quota")
    private String quota;

    @JsonProperty("IsSent")
    private String isSent;  // 0: <PERSON>h<PERSON><PERSON> đ<PERSON><PERSON><PERSON> gửi, 1: <PERSON><PERSON> gửi

    @JsonProperty("SendingTime")
    private String sendingTime;

    @JsonProperty("StatusDetail")
    private List<StatusDetails> statusDetail;
}
