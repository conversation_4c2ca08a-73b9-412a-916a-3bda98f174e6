package com.redon_agency.chatbot.common.feign.sms.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.io.Serializable;

@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AdsRequest implements Serializable {
    @JsonProperty("access_token")
    private String accessToken;

    @JsonProperty("session_id")
    private String sessionId;

    @JsonProperty("CampaignCode")
    private String campaignCode;

    @JsonProperty("PhoneList")
    private String phoneList;  // Danh sách số điện thoại, phân cách bằng dấu phẩy
}
