import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, Min, IsString, MaxLength } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * DTO cho việc tạo/cập nhật tồn kho sản phẩm từ user product
 * - Nếu truyền inventoryId: Sử dụng inventory đã có
 * - Nếu truyền đầy đủ thông tin khác: Tạo inventory mới
 */
export class ProductInventoryDto {
  /**
   * ID inventory hiện có (nếu sử dụng inventory có sẵn)
   * @example 123
   */
  @ApiProperty({
    description: 'ID inventory hiện có (nếu sử dụng inventory có sẵn)',
    example: 123,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID inventory phải là số' })
  @Type(() => Number)
  inventoryId?: number;

  /**
   * <PERSON>ại thao tác với inventory
   * @example "ADD"
   */
  @ApiProperty({
    description: 'Loại thao tác với inventory',
    enum: ['ADD', 'UPDATE'],
    example: 'UPDATE',
    required: false,
  })
  @IsOptional()
  @IsString()
  @IsIn(['ADD', 'UPDATE'], { message: 'Operation chỉ chấp nhận giá trị ADD hoặc UPDATE' })
  operation?: 'ADD' | 'UPDATE';

  /**
   * ID kho chứa sản phẩm
   * @example 1
   */
  @ApiProperty({
    description: 'ID kho chứa sản phẩm',
    example: 1,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'ID kho phải là số' })
  @Type(() => Number)
  warehouseId?: number;

  /**
   * Số lượng sẵn sàng để bán hoặc sử dụng (đây cũng là tổng số lượng)
   * @example 100
   */
  @ApiProperty({
    description: 'Số lượng sẵn sàng để bán hoặc sử dụng (đây cũng là tổng số lượng)',
    example: 100,
    required: false,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Số lượng sẵn sàng phải là số' })
  @Min(0, { message: 'Số lượng sẵn sàng phải lớn hơn hoặc bằng 0' })
  @Type(() => Number)
  availableQuantity?: number;

  /**
   * Mã SKU (Stock Keeping Unit) của sản phẩm trong kho
   * @example "SKU-001"
   */
  @ApiProperty({
    description: 'Mã SKU (Stock Keeping Unit) của sản phẩm trong kho',
    example: 'SKU-001',
    required: false,
    maxLength: 100,
  })
  @IsOptional()
  @IsString({ message: 'Mã SKU phải là chuỗi' })
  @MaxLength(100, { message: 'Mã SKU không được vượt quá 100 ký tự' })
  sku?: string;

  /**
   * Mã vạch (Barcode) của sản phẩm trong kho
   * @example "1234567890123"
   */
  @ApiProperty({
    description: 'Mã vạch (Barcode) của sản phẩm trong kho',
    example: '1234567890123',
    required: false,
    maxLength: 100,
  })
  @IsOptional()
  @IsString({ message: 'Mã vạch phải là chuỗi' })
  @MaxLength(100, { message: 'Mã vạch không được vượt quá 100 ký tự' })
  barcode?: string;
}
