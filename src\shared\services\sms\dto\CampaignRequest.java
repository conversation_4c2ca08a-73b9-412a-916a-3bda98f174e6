package com.redon_agency.chatbot.common.feign.sms.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.io.Serializable;

@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CampaignRequest implements Serializable {
    @JsonProperty("access_token")
    private String accessToken;

    @JsonProperty("session_id")
    private String sessionId;

    @JsonProperty("CampaignName")
    private String campaignName;  // Tên campaign không được trùng nhau

    @JsonProperty("BrandName")
    private String brandName;

    @JsonProperty("Message")
    private String message;       // Nội dung tin nhắn

    @JsonProperty("ScheduleTime")
    private String scheduleTime;  // Định dạng "yyyy-MM-dd HH:mm"

    @JsonProperty("Quota")
    private int quota;            // Hạn mức gửi tin của campaign
}
