package com.redon_agency.chatbot.common.feign.sms.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CancelAdsRequest {
    @JsonProperty("access_token")
    private String accessToken;

    @JsonProperty("session_id")
    private String sessionId;

    @JsonProperty("CampaignCode")
    private String campaignCode;
}
