import { Injectable } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { firstValueFrom } from 'rxjs';
import { BaseSmsProvider } from './base-sms-provider.service';
import {
  SmsResponse,
  MessageStatusResponse,
  ConnectionTestResponse,
  MessageStatus
} from './sms-provider.interface';

// TypeScript interfaces tương ứng với Java DTOs
export interface AccessTokenRequest {
  grant_type: string;
  client_id: string;
  client_secret: string;
  scope: string;
  session_id: string;
}

export interface AccessTokenResponse {
  access_token: string;
  expires_in: number;
  token_type: string;
  scope: string;
  error?: number;
  error_description?: string;
}

export interface OtpRequest {
  access_token: string;
  session_id: string;
  BrandName: string;
  Phone: string;
  Message: string;
}

export interface OtpResponse {
  MessageId: string;
  BrandName: string;
  Phone: string;
  Message: string;
  PartnerId: string;
  Telco: string;
}

export interface CampaignRequest {
  access_token: string;
  session_id: string;
  CampaignName: string;
  BrandName: string;
  Message: string;
  ScheduleTime: string; // Format: "yyyy-MM-dd HH:mm"
  Quota: number;
}

export interface CampaignResponse {
  CampaignCode: string;
  CampaignName: string;
  BrandName: string;
  Message: string;
  ScheduleTime: string;
  Quota: number;
  Status: string;
}

export interface AdsRequest {
  access_token: string;
  session_id: string;
  CampaignCode: string;
  PhoneList: string; // Danh sách số điện thoại phân cách bằng dấu phẩy
}

export interface AdsResponse {
  CampaignCode: string;
  TotalSent: number;
  SuccessCount: number;
  FailureCount: number;
  Details: Array<{
    Phone: string;
    Status: string;
    MessageId?: string;
    ErrorCode?: string;
    ErrorMessage?: string;
  }>;
}

export interface DetailsCampaignRequest {
  access_token: string;
  session_id: string;
  CampaignCode: string;
}

export interface DetailsCampaignResponse {
  CampaignCode: string;
  CampaignName: string;
  BrandName: string;
  Message: string;
  ScheduleTime: string;
  Quota: number;
  Status: string;
  TotalSent: number;
  SuccessCount: number;
  FailureCount: number;
  CreatedAt: string;
  UpdatedAt: string;
}

export interface DetailsStatusCampaignRequest {
  access_token: string;
  session_id: string;
  CampaignCode: string;
}

export interface CancelAdsRequest {
  access_token: string;
  session_id: string;
  CampaignCode: string;
}

export interface CancelAdsResponse {
  CampaignCode: string;
  Status: string;
  Message: string;
}

export interface DlrAdsRecheckRequest {
  access_token: string;
  session_id: string;
  FromDate: string; // Format: "yyyy-MM-dd"
  ToDate: string;   // Format: "yyyy-MM-dd"
}

export interface DlrAdsRecheckResponse {
  Campaigns: Array<{
    CampaignCode: string;
    CampaignName: string;
    TotalSent: number;
    ReceivedDlr: number;
    PendingDlr: number;
  }>;
}

export interface FprSmsConfig {
  apiUrl: string;
  clientId: string;
  clientSecret: string;
  brandName: string;
  sessionId?: string;
}

@Injectable()
export class FprSmsBrandnameService extends BaseSmsProvider {
  readonly providerName = 'FPR_SMS_BRANDNAME';
  private accessToken: string | null = null;
  private tokenExpiresAt: Date | null = null;

  constructor(
    private readonly httpService: HttpService,
    private readonly config: FprSmsConfig
  ) {
    super('FprSmsBrandnameService');
  }

  /**
   * Lấy access token từ FPR SMS API
   */
  async getAccessToken(): Promise<AccessTokenResponse> {
    try {
      const request: AccessTokenRequest = {
        grant_type: 'client_credentials',
        client_id: this.config.clientId,
        client_secret: this.config.clientSecret,
        scope: 'send_brandname_otp send_brandname_ads',
        session_id: this.config.sessionId || this.generateSessionId()
      };

      const response = await firstValueFrom(
        this.httpService.post<AccessTokenResponse>(
          `${this.config.apiUrl}/oauth2/token`,
          request,
          {
            headers: {
              'Content-Type': 'application/json'
            }
          }
        )
      );

      if (response.data.error) {
        throw new Error(`Token error: ${response.data.error_description}`);
      }

      // Lưu token và thời gian hết hạn
      this.accessToken = response.data.access_token;
      this.tokenExpiresAt = new Date(Date.now() + (response.data.expires_in * 1000));

      this.logger.log('Access token retrieved successfully');
      return response.data;
    } catch (error) {
      this.logger.error('Failed to get access token', error);
      throw error;
    }
  }