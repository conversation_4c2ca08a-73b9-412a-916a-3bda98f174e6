// Import các thư viện cần thiết từ NestJS
import { Injectable, Logger } from '@nestjs/common';

// Import các repository để tương tác với database
import {
  UserProductRepository,
  CustomFieldRepository,
  InventoryRepository,
  PhysicalWarehouseRepository,
  ProductAdvancedInfoRepository,
} from '@modules/business/repositories';

// Import các DTO cho business logic
import {
  BusinessUpdateProductDto,
  BusinessProductResponseDto as ProductResponseDto,
  QueryProductDto,
  BulkDeleteProductDto,
  BulkDeleteProductResponseDto,
  ProductInventoryDto,
  WarehouseListDto,
} from '../dto';

// Import DTO cho inventory
import {
  InventoryResponseDto,
  QueryInventoryDto,
} from '../dto/inventory';

// Import các enum định nghĩa trạng thái và loại sản phẩm
import { EntityStatusEnum, ProductTypeEnum } from '@modules/business/enums';

// Import exception handling
import { AppException } from '@common/exceptions/app.exception';
import { BUSINESS_ERROR_CODES } from '@modules/business/exceptions';

// Import các entity
import { Inventory } from '@modules/business/entities';

// Import decorator cho transaction
import { Transactional } from 'typeorm-transactional';

// Import utility cho data transformation
import { plainToInstance } from 'class-transformer';

// Import response wrapper
import { PaginatedResult } from '@common/response';

// Import các helper class
import { UserProductHelper } from '../helpers/user-product.helper';
import { MetadataHelper } from '../helpers/metadata.helper';
import { ValidationHelper } from '../helpers/validation.helper';
import { ProductValidationHelper } from '../helpers/product-validation.helper';

// Import các service cho S3 và CDN
import { S3Service } from '@shared/services/s3.service';
import { CdnService } from '@shared/services/cdn.service';

// Import utility cho S3 key generation
import { CategoryFolderEnum, generateS3Key } from '@shared/utils/generators/s3-key-generator.util';
import { FileSizeEnum, ImageTypeEnum, TimeIntervalEnum } from '@shared/utils';

// Import service cho classification
import { ClassificationService } from './classification.service';

// Import TypeORM DataSource
import { DataSource } from 'typeorm';

// Import processors
import { CreateProductOrchestrator } from './processors/create';
import { UpdateProductOrchestrator } from './processors/update';
import { CreatedProductDto } from '../dto/request/create-products.dto';



/**
 * Service xử lý logic nghiệp vụ cho sản phẩm của người dùng
 * Chịu trách nhiệm:
 * - Tạo, cập nhật, xóa sản phẩm
 * - Xử lý các loại sản phẩm khác nhau (Physical, Digital, Event, Service, Combo)
 * - Quản lý inventory, classifications, custom fields
 * - Xử lý upload hình ảnh lên S3
 * - Validate dữ liệu đầu vào
 */
@Injectable()
export class UserProductService {
  // Logger để ghi log các hoạt động của service
  private readonly logger = new Logger(UserProductService.name);

  constructor(
    // Repository để tương tác với bảng user_products
    private readonly userProductRepository: UserProductRepository,
    // Repository để tương tác với bảng custom_fields
    private readonly customFieldRepository: CustomFieldRepository,
    // Repository để tương tác với bảng inventories
    private readonly inventoryRepository: InventoryRepository,
    // Repository để tương tác với bảng physical_warehouses
    private readonly physicalWarehouseRepository: PhysicalWarehouseRepository,
    // Repository để tương tác với bảng product_advanced_info
    private readonly productAdvancedInfoRepository: ProductAdvancedInfoRepository,
    // Helper để xử lý logic liên quan đến UserProduct
    private readonly userProductHelper: UserProductHelper,
    // Helper để xử lý metadata và custom fields
    private readonly metadataHelper: MetadataHelper,
    // Service để tương tác với Amazon S3
    private readonly s3Service: S3Service,
    // Service để tạo CDN URLs
    private readonly cdnService: CdnService,
    // Service để xử lý classifications
    private readonly classificationService: ClassificationService,
    // Helper để validate dữ liệu
    private readonly validationHelper: ValidationHelper,
    // Helper để validate thông tin sản phẩm
    private readonly productValidationHelper: ProductValidationHelper,
    // DataSource để thực hiện raw queries
    private readonly dataSource: DataSource,
    // Orchestrator để xử lý tạo sản phẩm
    private readonly createProductOrchestrator: CreateProductOrchestrator,
    // Orchestrator để xử lý cập nhật sản phẩm
    private readonly updateProductOrchestrator: UpdateProductOrchestrator,
  ) {}

  /**
   * Tạo sản phẩm mới với type-safe handling
   * Hỗ trợ tất cả các loại sản phẩm: Physical, Digital, Event, Service, Combo
   * @param createProductDto DTO chứa thông tin sản phẩm mới
   * @param userId ID của người dùng hiện tại
   * @returns Thông tin sản phẩm đã tạo kèm theo upload URLs và thông tin bổ sung
   */
  @Transactional()
  async createProduct(
    createProductDto: CreatedProductDto,
    userId: number,
  ): Promise<ProductResponseDto> {
    // Delegate việc tạo sản phẩm cho CreateProductOrchestrator
    return await this.createProductOrchestrator.createProduct(createProductDto, userId);
  }

  /**
   * Lấy danh sách sản phẩm với phân trang và filter
   * Chức năng:
   * - Lấy danh sách sản phẩm từ repository với các filter
   * - Chuyển đổi entity sang DTO response
   * - Trả về kết quả với pagination metadata
   * @param queryDto DTO chứa các tham số truy vấn (filter, sort, pagination)
   * @returns Danh sách sản phẩm với phân trang
   */
  async getProducts(
    queryDto: QueryProductDto,
  ): Promise<PaginatedResult<ProductResponseDto>> {
    try {
      // Lấy danh sách sản phẩm từ repository với filter và pagination
      const productsResult =
        await this.userProductRepository.findProducts(queryDto);

      // Chuyển đổi từ entity sang DTO response song song để tăng hiệu suất
      const items = await Promise.all(
        productsResult.items.map((product) =>
          this.userProductHelper.mapToProductResponseDto(product),
        ),
      );

      // Trả về kết quả với pagination metadata
      return {
        items,
        meta: productsResult.meta,
      };
    } catch (error) {
      // Nếu là AppException, ném lại để giữ nguyên error code
      if (error instanceof AppException) {
        throw error;
      }

      // Nếu là lỗi khác, ghi log chi tiết và wrap thành AppException
      this.logger.error(
        `Lỗi khi lấy danh sách sản phẩm: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_FIND_FAILED,
        `Lỗi khi lấy danh sách sản phẩm: ${error.message}`,
      );
    }
  }

  /**
   * Lấy chi tiết sản phẩm theo ID với đầy đủ thông tin liên quan
   * Chức năng:
   * - Lấy thông tin sản phẩm cơ bản
   * - Lấy classifications (phân loại)
   * - Lấy inventory (chỉ cho sản phẩm PHYSICAL)
   * - Lấy thông tin warehouse (nếu có)
   * @param id ID của sản phẩm
   * @returns Chi tiết sản phẩm với đầy đủ thông tin
   */
  async getProductDetail(id: number): Promise<ProductResponseDto> {
    try {
      // Tìm sản phẩm theo ID
      const product = await this.userProductRepository.findById(id);

      // Kiểm tra sản phẩm tồn tại
      if (!product) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND,
          `Không tìm thấy sản phẩm với ID ${id}`,
        );
      }

      // Chuyển đổi từ entity sang DTO response
      const productDto = await this.userProductHelper.mapToProductResponseDto(product);

      // Lấy danh sách phân loại sản phẩm nếu có
      try {
        const classifications = await this.classificationService.getByProductId(id);
        if (classifications && classifications.length > 0) {
          // Thêm danh sách phân loại vào DTO
          productDto['classifications'] = classifications;
        }
      } catch (classificationError) {
        // Ghi log warning nhưng không dừng việc trả về sản phẩm
        this.logger.warn(`Không thể lấy danh sách phân loại cho sản phẩm ${id}: ${classificationError.message}`);
      }

      // Lấy thông tin tồn kho chỉ cho sản phẩm PHYSICAL
      // Các loại sản phẩm khác (DIGITAL, SERVICE, EVENT, COMBO) không có inventory
      if (product.productType === ProductTypeEnum.PHYSICAL) {
        try {
          this.logger.log(`DEBUG: Lấy inventory cho sản phẩm PHYSICAL ID: ${id}`);
          // Lấy TẤT CẢ inventory của sản phẩm (có thể có nhiều inventory cho các kho khác nhau)
          const inventoriesResult = await this.inventoryRepository.findAll({ productId: id });
          this.logger.log(`DEBUG: Tìm thấy ${inventoriesResult.items?.length || 0} inventory cho sản phẩm ${id}`);

          if (inventoriesResult.items && inventoriesResult.items.length > 0) {
            const inventoryDtos: InventoryResponseDto[] = [];

            // Xử lý từng inventory
            for (const inventory of inventoriesResult.items) {
              // Chuyển đổi sang DTO response
              const inventoryDto = plainToInstance(InventoryResponseDto, inventory, {
                excludeExtraneousValues: true,
              });

              // Lấy thông tin warehouse nếu có
              if (inventory.warehouseId) {
                try {
                  const warehouse = await this.physicalWarehouseRepository.findByWarehouseIdWithDetails(inventory.warehouseId);
                  if (warehouse) {
                    // Thêm thông tin warehouse vào inventory DTO
                    inventoryDto.warehouse = {
                      id: warehouse.id,
                      warehouseId: warehouse.warehouseId,
                      name: warehouse.name,
                      description: warehouse.description,
                      type: warehouse.type,
                      address: warehouse.address,
                      capacity: warehouse.capacity,
                    };
                  }
                } catch (warehouseError) {
                  this.logger.warn(`Không thể lấy thông tin warehouse cho inventory ${inventory.id}: ${warehouseError.message}`);
                }
              }

              inventoryDtos.push(inventoryDto);
            }

            // Thêm TẤT CẢ inventory vào response như ARRAY
            productDto['inventory'] = inventoryDtos;
            this.logger.log(`Trả về ${inventoryDtos.length} inventory cho sản phẩm ${id}`);
          }
        } catch (inventoryError) {
          // Ghi log warning nhưng không dừng việc trả về sản phẩm
          this.logger.warn(`Không thể lấy thông tin tồn kho cho sản phẩm ${id}: ${inventoryError.message}`);
        }
      }

      return productDto;
    } catch (error) {
      // Nếu là AppException, ném lại để giữ nguyên error code
      if (error instanceof AppException) {
        throw error;
      }

      // Nếu là lỗi khác, ghi log chi tiết và wrap thành AppException
      this.logger.error(
        `Lỗi khi lấy chi tiết sản phẩm: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_FIND_FAILED,
        `Lỗi khi lấy chi tiết sản phẩm: ${error.message}`,
      );
    }
  }

  /**
   * Tạo nhiều sản phẩm cùng lúc
   * Sử dụng service tạo 1 sản phẩm để đảm bảo tính nhất quán
   * @param batchCreateDto DTO chứa danh sách sản phẩm cần tạo
   * @param userId ID của người dùng hiện tại
   * @returns Kết quả tạo batch với thông tin thành công và thất bại
   */
  @Transactional()
  async batchCreateProducts(
    batchCreateDto: { products: CreatedProductDto[] },
    userId: number,
  ): Promise<{
    successProducts: ProductResponseDto[];
    failedProducts: Array<{
      index: number;
      productName: string;
      error: string;
    }>;
    totalProducts: number;
    successCount: number;
    failedCount: number;
  }> {
    try {
      // Validate input
      if (!batchCreateDto?.products || !Array.isArray(batchCreateDto.products)) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          'Danh sách sản phẩm không hợp lệ',
        );
      }

      if (batchCreateDto.products.length === 0) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          'Danh sách sản phẩm không được rỗng',
        );
      }

      // Giới hạn số lượng sản phẩm có thể tạo cùng lúc để tránh timeout
      const MAX_BATCH_SIZE = 50;
      if (batchCreateDto.products.length > MAX_BATCH_SIZE) {
        throw new AppException(
          BUSINESS_ERROR_CODES.INVALID_INPUT,
          `Chỉ có thể tạo tối đa ${MAX_BATCH_SIZE} sản phẩm cùng lúc`,
        );
      }

      this.logger.log(`Bắt đầu tạo batch ${batchCreateDto.products.length} sản phẩm cho userId=${userId}`);

      const successProducts: ProductResponseDto[] = [];
      const failedProducts: Array<{
        index: number;
        productName: string;
        error: string;
      }> = [];

      // Xử lý từng sản phẩm một cách tuần tự để tránh conflict và đảm bảo transaction integrity
      for (let i = 0; i < batchCreateDto.products.length; i++) {
        const productDto = batchCreateDto.products[i];

        try {
          // Validate basic product data
          if (!productDto?.name) {
            throw new AppException(
              BUSINESS_ERROR_CODES.INVALID_INPUT,
              'Tên sản phẩm không được để trống',
            );
          }

          if (!productDto?.productType) {
            throw new AppException(
              BUSINESS_ERROR_CODES.INVALID_INPUT,
              'Loại sản phẩm không được để trống',
            );
          }

          this.logger.log(`Đang tạo sản phẩm ${i + 1}/${batchCreateDto.products.length}: ${productDto.name}`);

          // Sử dụng trực tiếp service createProduct để đảm bảo tính nhất quán
          // Service này đã được tối ưu và có đầy đủ validation, error handling
          const createdProduct = await this.createProduct(productDto, userId);

          successProducts.push(createdProduct);
          this.logger.log(`Tạo thành công sản phẩm: ${productDto.name} (ID: ${createdProduct.id})`);

        } catch (error) {
          this.logger.error(
            `Lỗi khi tạo sản phẩm ${productDto?.name || 'Unknown'} tại vị trí ${i}: ${error.message}`,
            error.stack,
          );

          // Lấy thông điệp lỗi từ AppException hoặc lỗi khác
          const errorMessage = error instanceof AppException
            ? error.message
            : `Lỗi không xác định: ${error.message}`;

          failedProducts.push({
            index: i,
            productName: productDto?.name || 'Unknown',
            error: errorMessage,
          });
        }
      }

      const result = {
        successProducts,
        failedProducts,
        totalProducts: batchCreateDto.products.length,
        successCount: successProducts.length,
        failedCount: failedProducts.length,
      };

      this.logger.log(
        `Hoàn thành batch create: ${result.successCount}/${result.totalProducts} thành công, ${result.failedCount} thất bại`,
      );

      return result;

    } catch (error) {
      // Nếu là AppException, ném lại để giữ nguyên error code
      if (error instanceof AppException) {
        throw error;
      }

      // Nếu là lỗi khác, ghi log chi tiết và wrap thành AppException
      this.logger.error(`Lỗi khi tạo batch sản phẩm: ${error.message}`, error.stack);
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_CREATION_FAILED,
        `Lỗi khi tạo batch sản phẩm: ${error.message}`,
      );
    }
  }

  /**
   * Cập nhật sản phẩm
   * @param id ID của sản phẩm cần cập nhật
   * @param updateProductDto DTO chứa thông tin cập nhật
   * @param userId ID của người dùng hiện tại
   * @returns Thông tin sản phẩm đã cập nhật
   */
  @Transactional()
  async updateProduct(
    id: number,
    updateProductDto: BusinessUpdateProductDto,
    userId: number,
  ): Promise<ProductResponseDto> {
    this.logger.log(`Delegating product update to orchestrator - ID: ${id}, User: ${userId}`);

    // Sử dụng UpdateProductOrchestrator để xử lý toàn bộ luồng update
    return await this.updateProductOrchestrator.updateProduct(id, updateProductDto, userId);
  }

  /**
   * Xóa sản phẩm (soft delete)
   * @param id ID của sản phẩm cần xóa
   * @param userId ID của người dùng hiện tại
   */
  @Transactional()
  async deleteProduct(id: number, userId: number): Promise<void> {
    try {
      // Tìm sản phẩm theo ID và ID người dùng
      const product = await this.userProductRepository.findByIdAndUserId(
        id,
        userId,
      );

      // Kiểm tra sản phẩm tồn tại
      if (!product) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND,
          `Không tìm thấy sản phẩm với ID ${id}`,
        );
      }

      // Cập nhật trạng thái sản phẩm thành DELETED
      product.status = EntityStatusEnum.DELETED;
      product.updatedAt = Date.now();

      // Lưu sản phẩm vào database
      await this.userProductRepository.save(product);
    } catch (error) {
      // Nếu là AppException, ném lại
      if (error instanceof AppException) {
        throw error;
      }

      // Nếu là lỗi khác, ghi log và ném lỗi chung
      this.logger.error(`Lỗi khi xóa sản phẩm: ${error.message}`, error.stack);
      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_DELETION_FAILED,
        `Lỗi khi xóa sản phẩm: ${error.message}`,
      );
    }
  }

  /**
   * Xóa nhiều sản phẩm (soft delete)
   * @param bulkDeleteDto DTO chứa danh sách ID sản phẩm cần xóa
   * @param userId ID của người dùng hiện tại
   * @returns Kết quả xóa nhiều sản phẩm
   */
  @Transactional()
  async bulkDeleteProducts(
    bulkDeleteDto: BulkDeleteProductDto,
    userId: number,
  ): Promise<BulkDeleteProductResponseDto> {
    try {
      const { productIds } = bulkDeleteDto;
      const results: any[] = [];
      let successCount = 0;
      let failureCount = 0;

      this.logger.log(
        `Bắt đầu xóa bulk ${productIds.length} sản phẩm cho userId=${userId}`,
      );

      // Xử lý từng sản phẩm một để có thể báo cáo chi tiết
      for (const productId of productIds) {
        try {
          // Tìm sản phẩm theo ID và ID người dùng
          const product = await this.userProductRepository.findByIdAndUserId(
            productId,
            userId,
          );

          // Kiểm tra sản phẩm tồn tại
          if (!product) {
            results.push({
              productId,
              status: 'error',
              message: `Không tìm thấy sản phẩm với ID ${productId}`,
            });
            failureCount++;
            continue;
          }

          // Cập nhật trạng thái sản phẩm thành DELETED
          product.status = EntityStatusEnum.DELETED;
          product.updatedAt = Date.now();

          // Lưu sản phẩm vào database
          await this.userProductRepository.save(product);

          results.push({
            productId,
            status: 'success',
            message: 'Xóa sản phẩm thành công',
          });
          successCount++;

        } catch (error) {
          this.logger.error(
            `Lỗi khi xóa sản phẩm ${productId}: ${error.message}`,
            error.stack,
          );

          results.push({
            productId,
            status: 'error',
            message: error instanceof AppException ? error.message : `Lỗi khi xóa sản phẩm: ${error.message}`,
          });
          failureCount++;
        }
      }

      const response: BulkDeleteProductResponseDto = {
        totalRequested: productIds.length,
        successCount,
        failureCount,
        results,
        message: `Xóa thành công ${successCount}/${productIds.length} sản phẩm`,
      };

      this.logger.log(
        `Hoàn thành xóa bulk sản phẩm: ${successCount} thành công, ${failureCount} thất bại`,
      );

      return response;
    } catch (error) {
      this.logger.error(
        `Lỗi khi xóa bulk sản phẩm: ${error.message}`,
        error.stack,
      );

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        BUSINESS_ERROR_CODES.PRODUCT_DELETION_FAILED,
        `Lỗi khi xóa bulk sản phẩm: ${error.message}`,
      );
    }
  }

  // ==================== INVENTORY MANAGEMENT METHODS ====================

  /**
   * Lấy thông tin tồn kho của sản phẩm theo kho
   * @param productId ID sản phẩm
   * @param warehouseId ID kho (optional)
   * @param userId ID người dùng
   * @returns Thông tin tồn kho
   */
  @Transactional()
  async getProductInventory(
    productId: number,
    warehouseId: number | null,
    userId: number,
  ): Promise<InventoryResponseDto[]> {
    try {
      // Kiểm tra sản phẩm có tồn tại và thuộc về user
      const product = await this.userProductRepository.findByIdAndUserId(productId, userId);
      if (!product) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND,
          'Sản phẩm không tồn tại hoặc không thuộc về bạn',
        );
      }

      // Tạo query để lấy inventory
      const queryDto = new QueryInventoryDto();
      queryDto.productId = productId;
      queryDto.userId = userId;
      if (warehouseId) {
        queryDto.warehouseId = warehouseId;
      }

      // Lấy danh sách inventory
      const inventoryResult = await this.inventoryRepository.findAll(queryDto);

      // Chuyển đổi sang DTO response
      const inventoryDtos = await Promise.all(
        inventoryResult.items.map(async (inventory) => {
          const dto = new InventoryResponseDto();
          dto.id = inventory.id;
          dto.productId = inventory.productId;
          dto.warehouseId = inventory.warehouseId;
          dto.currentQuantity = inventory.currentQuantity;
          dto.totalQuantity = inventory.totalQuantity;
          dto.availableQuantity = inventory.availableQuantity;
          dto.reservedQuantity = inventory.reservedQuantity;
          dto.defectiveQuantity = inventory.defectiveQuantity;
          dto.lastUpdated = inventory.lastUpdated;

          // Lấy thông tin warehouse nếu có
          if (inventory.warehouseId) {
            const warehouse = await this.physicalWarehouseRepository.findByWarehouseIdWithDetails(inventory.warehouseId);
            if (warehouse) {
              dto.warehouse = {
                id: warehouse.id,
                warehouseId: warehouse.warehouseId,
                name: warehouse.name,
                description: warehouse.description,
                type: warehouse.type,
                address: warehouse.address,
                capacity: warehouse.capacity,
              };
            }
          }

          return dto;
        })
      );

      return inventoryDtos;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Lỗi khi lấy thông tin tồn kho sản phẩm ${productId}: ${error.message}`, error.stack);
      throw new AppException(
        BUSINESS_ERROR_CODES.INVENTORY_FETCH_FAILED,
        `Lỗi khi lấy thông tin tồn kho: ${error.message}`,
      );
    }
  }

  /**
   * Tạo hoặc cập nhật tồn kho cho sản phẩm
   * @param productId ID sản phẩm
   * @param inventoryData Dữ liệu tồn kho
   * @param userId ID người dùng
   * @returns Thông tin tồn kho đã tạo/cập nhật
   */
  @Transactional()
  async createOrUpdateProductInventory(
    productId: number,
    inventoryData: ProductInventoryDto,
    userId: number,
  ): Promise<InventoryResponseDto> {
    try {
      // Kiểm tra sản phẩm có tồn tại và thuộc về user
      const product = await this.userProductRepository.findByIdAndUserId(productId, userId);
      if (!product) {
        throw new AppException(
          BUSINESS_ERROR_CODES.PRODUCT_NOT_FOUND,
          'Sản phẩm không tồn tại hoặc không thuộc về bạn',
        );
      }

      // Kiểm tra warehouse có tồn tại
      if (inventoryData.warehouseId) {
        this.logger.log(`Kiểm tra warehouse với ID: ${inventoryData.warehouseId}`);

        // Kiểm tra warehouse tồn tại trong bảng warehouse trước
        const warehouseGeneral = await this.dataSource.getRepository('warehouse')
          .createQueryBuilder('w')
          .where('w.warehouse_id = :warehouseId', { warehouseId: inventoryData.warehouseId })
          .getOne();

        if (!warehouseGeneral) {
          throw new AppException(
            BUSINESS_ERROR_CODES.WAREHOUSE_NOT_FOUND,
            `Kho với ID ${inventoryData.warehouseId} không tồn tại trong hệ thống`,
          );
        }

        this.logger.log(`Warehouse general tồn tại: ${JSON.stringify(warehouseGeneral)}`);

        // Kiểm tra physical warehouse
        const warehouse = await this.physicalWarehouseRepository.findByWarehouseId_user(inventoryData.warehouseId);
        if (!warehouse) {
          throw new AppException(
            BUSINESS_ERROR_CODES.WAREHOUSE_NOT_FOUND,
            `Kho vật lý với ID ${inventoryData.warehouseId} không tồn tại trong bảng physical_warehouse`,
          );
        }

        this.logger.log(`Physical warehouse tồn tại: ${JSON.stringify(warehouse)}`);
      }

      // Kiểm tra SKU không được trùng với sản phẩm khác của user đang đăng nhập
      if (inventoryData.sku) {
        const existingInventoryWithSku = await this.inventoryRepository.findBySkuAndUserId(inventoryData.sku, userId);
        if (existingInventoryWithSku && existingInventoryWithSku.productId !== productId) {
          throw new AppException(
            BUSINESS_ERROR_CODES.INVENTORY_CREATION_FAILED,
            `Mã SKU "${inventoryData.sku}" đã tồn tại trong sản phẩm khác của bạn`,
          );
        }
      }

      // Kiểm tra xem đã có inventory cho sản phẩm và kho này chưa
      const existingInventory = await this.inventoryRepository.findByProductAndWarehouseNullable(
        productId,
        inventoryData.warehouseId || null,
      );

      let inventory: Inventory;

      if (existingInventory) {
        // Cập nhật inventory hiện có
        existingInventory.availableQuantity = inventoryData.availableQuantity || 0;
        existingInventory.sku = inventoryData.sku || null;
        existingInventory.barcode = inventoryData.barcode || null;

        // Sử dụng helper để tính toán số lượng
        this.validationHelper.calculateInventoryQuantities(existingInventory);
        existingInventory.lastUpdated = Date.now();

        inventory = await this.inventoryRepository.save(existingInventory);
      } else {
        // Tạo inventory mới
        const newInventory = new Inventory();
        newInventory.productId = productId;
        newInventory.warehouseId = inventoryData.warehouseId || null;
        newInventory.availableQuantity = inventoryData.availableQuantity || 0;
        newInventory.sku = inventoryData.sku || null;
        newInventory.barcode = inventoryData.barcode || null;

        // Sử dụng helper để tính toán số lượng
        this.validationHelper.calculateInventoryQuantities(newInventory);
        newInventory.lastUpdated = Date.now();

        inventory = await this.inventoryRepository.save(newInventory);
      }

      // Chuyển đổi sang DTO response
      const dto = new InventoryResponseDto();
      dto.id = inventory.id;
      dto.productId = inventory.productId;
      dto.warehouseId = inventory.warehouseId;
      dto.currentQuantity = inventory.currentQuantity;
      dto.totalQuantity = inventory.totalQuantity;
      dto.availableQuantity = inventory.availableQuantity;
      dto.reservedQuantity = inventory.reservedQuantity;
      dto.defectiveQuantity = inventory.defectiveQuantity;
      dto.lastUpdated = inventory.lastUpdated;
      dto.sku = inventory.sku;
      dto.barcode = inventory.barcode;

      // Lấy thông tin warehouse nếu có
      if (inventory.warehouseId) {
        const warehouse = await this.physicalWarehouseRepository.findByWarehouseIdWithDetails(inventory.warehouseId);
        if (warehouse) {
          dto.warehouse = {
            id: warehouse.id,
            warehouseId: warehouse.warehouseId,
            name: warehouse.name,
            description: warehouse.description,
            type: warehouse.type,
            address: warehouse.address,
            capacity: warehouse.capacity,
          };
        }
      }

      return dto;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Lỗi khi tạo/cập nhật tồn kho sản phẩm ${productId}: ${error.message}`, error.stack);
      throw new AppException(
        BUSINESS_ERROR_CODES.INVENTORY_CREATION_FAILED,
        `Lỗi khi tạo/cập nhật tồn kho: ${error.message}`,
      );
    }
  }

  /**
   * Lấy danh sách kho vật lý để chọn
   * @returns Danh sách kho vật lý
   */
  async getWarehouseList(): Promise<WarehouseListDto[]> {
    try {
      // Lấy tất cả kho vật lý với thông tin đầy đủ
      const result = await this.physicalWarehouseRepository.findAll({
        page: 1,
        limit: 1000, // Lấy nhiều để có đủ kho cho user chọn
        sortBy: 'warehouseId',
        sortDirection: 'ASC',
      });

      // Chuyển đổi sang DTO
      return result.items.map(warehouse => {
        const dto = new WarehouseListDto();
        dto.warehouseId = warehouse.warehouseId;
        dto.name = warehouse.name;
        dto.description = warehouse.description;
        dto.type = warehouse.type;
        dto.address = warehouse.address;
        dto.capacity = warehouse.capacity;
        return dto;
      });
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách kho vật lý: ${error.message}`, error.stack);
      throw new AppException(
        BUSINESS_ERROR_CODES.WAREHOUSE_FIND_FAILED,
        `Lỗi khi lấy danh sách kho vật lý: ${error.message}`,
      );
    }
  }
}