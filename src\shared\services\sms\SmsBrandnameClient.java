package com.redon_agency.chatbot.common.feign.sms;

import com.redon_agency.chatbot.common.feign.sms.dto.*;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "smsBrandnameClient", url = "${sms.host.api}")
public interface SmsBrandnameClient {
    // API Lấy Access Token
    @PostMapping(value = "/oauth2/token", consumes = "application/json")
    AccessTokenResponse getAccessToken(@RequestBody AccessTokenRequest request);

    // API Gửi Tin Nhắn Brandname OTP
    @PostMapping(value = "/api/push-brandname-otp", consumes = "application/json")
    OtpResponse sendOtp(@RequestBody OtpRequest request);

    // API Tạo Campaign Quảng Cáo
    @PostMapping(value = "/api/create-campaign", consumes = "application/json")
    CampaignResponse createCampaign(@RequestBody CampaignRequest request);

    // API Xem Chi Tiết Campaign Quảng Cáo
    @PostMapping(value = "/api/detail-ads", consumes = "application/json")
    DetailsCampaignRes detailsCampaign(@RequestBody DetailsCampaignReq request);

    // API Lấy Trạng Thái Của Từng Tin Quảng Cáo
    @PostMapping(value = "/api/dlr-ads", consumes = MediaType.APPLICATION_JSON_VALUE, produces = MediaType.APPLICATION_OCTET_STREAM_VALUE)
    ResponseEntity<byte[]> detailsStatusCampaign(@RequestBody DetailsStatusCampaignReq request);

    // API Gửi Tin Nhắn Quảng Cáo
    @PostMapping(value = "/api/push-brandname-ads", consumes = "application/json")
    AdsResponse sendAds(@RequestBody AdsRequest request);

    // API Hủy Tin Nhắn Quảng Cáo
    @PostMapping(value = "/api/cancel-ads", consumes = "application/json")
    CancelAdsResponse cancelAds(@RequestBody CancelAdsRequest request);

    // API Lấy danh sách campaign QC chưa nhận đủ DLR
    @PostMapping(value = "/dlr-ads-recheck", consumes = "application/json")
    ResponseEntity<DlrAdsRecheckRes> getDlrAdsRecheck(@RequestBody DlrAdsRecheckReq request);
}