package com.redon_agency.chatbot.common.feign.sms.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.io.Serializable;

@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DetailsStatusCampaignReq implements Serializable {
    @JsonProperty("access_token")
    private String accessToken;

    @JsonProperty("campaign_code")
    private String campaignCode;
}
