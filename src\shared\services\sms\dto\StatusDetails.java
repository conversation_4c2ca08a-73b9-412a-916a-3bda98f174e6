package com.redon_agency.chatbot.common.feign.sms.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.io.Serializable;

/**
 * Status 0: <PERSON><PERSON><PERSON><PERSON> đư<PERSON><PERSON> approve
 * Status 1: <PERSON><PERSON><PERSON><PERSON> approve
 * Reason: <PERSON><PERSON> do bị từ chôi
 */
@Setter
@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StatusDetails implements Serializable {
    @JsonProperty("Status")
    private String status;

    @JsonProperty("Reason")
    private String reason;
}
